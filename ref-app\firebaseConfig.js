import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';

// Firebase configuration object
// Get these values from Firebase Console > Project Settings > General > Your apps > Web app
const firebaseConfig = {
  apiKey: "AIzaSyBD0OU7EE_g4u1tWTwiEnkQj3vDgWkb5LM",
  authDomain: "ref-app123.firebaseapp.com",
  projectId: "ref-app123",
  storageBucket: "ref-app123.firebasestorage.app",
  messagingSenderId: "723844449983",
  appId: "1:723844449983:web:7e5deea947e7a07d31e80a",
  measurementId: "G-6J5ME2BYT1"                // Optional: Google Analytics measurement ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app);

// Export the app instance if needed elsewhere
export default app;

/*
HOW TO GET YOUR FIREBASE CONFIG:
1. Go to https://console.firebase.google.com/
2. Select your project (or create a new one)
3. Click on "Project Settings" (gear icon)
4. Scroll down to "Your apps" section
5. Click "Add app" and select "Web" (</>) if you haven't already
6. Register your app with a nickname (e.g., "Referee App")
7. Copy the config object values and replace the placeholders above

IMPORTANT SECURITY NOTES:
- These values are safe to expose in client-side code
- The apiKey is not a secret - it's used to identify your project
- Security is handled by Firebase Security Rules, not by hiding the config
- Never put server-side Firebase Admin SDK keys in client code
*/
